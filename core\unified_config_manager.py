#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一配置管理器
整合所有配置文件，提供统一的配置加载和管理接口
消除配置重复，简化配置管理
集成数据路径管理功能
"""

import os
import yaml
import json
import threading
from typing import Dict, Any, Optional, Union, List
from pathlib import Path
from loguru import logger
from dataclasses import dataclass
from enum import Enum
from datetime import datetime

try:
    from .environment_manager import get_environment_manager
    ENVIRONMENT_MANAGER_AVAILABLE = True
except ImportError:
    ENVIRONMENT_MANAGER_AVAILABLE = False


# 数据层级和类型定义 - 整合自 unified_data_config.py
class DataLayer(Enum):
    """数据层级枚举"""
    RAW = "01_raw"
    PROCESSED = "02_processed"
    ENHANCED = "03_enhanced"
    PRODUCTION = "04_production"
    OUTPUTS = "05_outputs"
    METADATA = "metadata"


class DataType(Enum):
    """数据类型枚举"""
    # 原始数据类型
    EQUIPMENT = "equipment"
    FAULT_CASES = "fault_cases"
    SENSOR_DATA = "sensor_data"
    MAINTENANCE_LOGS = "maintenance_logs"
    INSPECTION_REPORTS = "inspection_reports"
    UPLOADS = "uploads"

    # 处理数据类型
    CLEANED = "cleaned"
    
    def get_dict(self) -> dict:
        """获取配置字典
        Returns:
            Dict: 配置字典
        """
        if hasattr(self, '_config_dict'):
            return self._config_dict
        return {}
    ANNOTATED = "annotated"
    STRUCTURED = "structured"
    VALIDATED = "validated"

    # 增强数据类型
    GENERATED = "generated"
    INTEGRATED = "integrated"
    KNOWLEDGE_BASE = "knowledge_base"
    TRAINING_SETS = "training_sets"

    # 生产数据类型
    ACTIVE = "active"
    CACHED = "cached"
    INDEXED = "indexed"
    BACKUPS = "backups"

    # 输出数据类型
    REPORTS = "reports"
    EXPORTS = "exports"
    VISUALIZATIONS = "visualizations"
    LOGS = "logs"


@dataclass
class ConfigChangeEvent:
    """配置变更事件"""
    key: str
    old_value: Any
    new_value: Any
    timestamp: datetime
    source: str = "manual"


@dataclass
class DataPath:
    """数据路径配置"""
    layer: DataLayer
    data_type: DataType
    description: str
    read_only: bool = False
    auto_create: bool = True


class UnifiedConfigManager:
    """统一配置管理器"""
    
    _instance = None
    _config = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            # 设置项目根目录
            self.project_root = Path(__file__).parent.parent

            # 初始化配置变更监听器
            self._change_listeners = []
            self._config_history = []
            self._lock = threading.RLock()

            # 初始化环境管理器
            if ENVIRONMENT_MANAGER_AVAILABLE:
                self.env_manager = get_environment_manager()
            else:
                self.env_manager = None

            # 标记为已初始化
            UnifiedConfigManager._initialized = True
            self.load_config()
    
    def load_config(self, config_path: Optional[str] = None) -> None:
        """加载配置文件"""
        if config_path is None:
            config_path = self._find_main_config()
        
        try:
            # 加载主配置文件
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                    self._config = yaml.safe_load(f)
                else:
                    self._config = json.load(f)
            
            # 处理环境变量替换
            self._process_env_variables(self._config)
            
            # 验证配置
            self._validate_config()
            
            logger.info(f"统一配置加载成功: {config_path}")
            
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            self._config = self._get_default_config()
    
    def _find_main_config(self) -> str:
        """查找主配置文件"""
        possible_paths = [
            "configs/config.yaml",
            "config.yaml",
            "configs/main_config.yaml"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # 如果没有找到，创建默认配置
        return self._create_default_config()
    
    def _create_default_config(self) -> str:
        """创建默认配置文件"""
        config_path = "configs/config.yaml"
        os.makedirs("configs", exist_ok=True)
        
        default_config = self._get_default_config()
        
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"创建默认配置文件: {config_path}")
        return config_path
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "system": {
                "name": "故障分析智能助手",
                "version": "2.0.0",
                "debug": False,
                "log_level": "INFO",
                "environment": "development"
            },
            "server": {
                "host": "0.0.0.0",
                "port": 5002,
                "workers": 4,
                "cors_origins": ["http://localhost:5002", "http://127.0.0.1:5002"]
            },
            "api": {
                "deepseek": {
                    "api_key": "${DEEPSEEK_API_KEY}",
                    "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
                    "r1_model": "deepseek-r1",
                    "chat_model": "deepseek-v3",
                    "max_tokens": 8192,
                    "temperature": 0.1
                }
            },
            "database": {
                "url": "sqlite:///./fault_diagnosis.db",
                "echo": False
            },
            "vector_db": {
                "type": "chroma",
                "chroma": {
                    "persist_directory": "./embeddings/chroma_store_new",
                    "collection_name": "baiyin_power_fault_collection"
                }
            },
            "upload": {
                "max_file_size": 100,
                "allowed_extensions": [".pdf", ".docx", ".txt", ".jpg", ".png"],
                "upload_path": "./uploads"
            },
            "security": {
                "secret_key": "${SECRET_KEY}",
                "csrf_protection": True,
                "rate_limiting": True,
                "max_requests_per_minute": 100
            },
            "logging": {
                "level": "INFO",
                "file_path": "./logs/app.log",
                "rotation": "1 day",
                "retention": "30 days"
            }
        }
    
    def _process_env_variables(self, config: Dict[str, Any]) -> None:
        """处理环境变量替换"""
        def replace_env_vars(obj):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    obj[key] = replace_env_vars(value)
            elif isinstance(obj, list):
                return [replace_env_vars(item) for item in obj]
            elif isinstance(obj, str) and obj.startswith("${") and obj.endswith("}"):
                env_var = obj[2:-1]
                return os.getenv(env_var, obj)
            return obj
        
        replace_env_vars(config)
    
    def _validate_config(self) -> None:
        """验证配置完整性"""
        required_keys = [
            "system.name",
            "server.host",
            "server.port"
        ]
        
        for key in required_keys:
            if self.get(key) is None:
                logger.warning(f"缺少配置项: {key}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值，支持点号分隔的嵌套键"""
        if self._config is None:
            return default
        
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """设置配置值"""
        if self._config is None:
            self._config = {}
        
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """获取配置段"""
        return self.get(section, {})
    
    def is_debug(self) -> bool:
        """是否为调试模式"""
        return self.get("system.debug", False)
    
    def get_api_key(self, service: str = "deepseek") -> Optional[str]:
        """获取API密钥"""
        return self.get(f"api.{service}.api_key")
    
    def get_cors_origins(self) -> list:
        """获取CORS允许的源"""
        return self.get("server.cors_origins", [])

    def set_config(self, key: str, value: Any, source: str = "manual") -> None:
        """设置配置值并触发变更事件"""
        with self._lock:
            old_value = self.get(key)

            # 设置新值
            self.set(key, value)

            # 记录变更事件
            event = ConfigChangeEvent(
                key=key,
                old_value=old_value,
                new_value=value,
                timestamp=datetime.now(),
                source=source
            )
            self._config_history.append(event)

            # 通知监听器
            self._notify_listeners(event)

            logger.info(f"配置已更新: {key} = {value}")

    def add_change_listener(self, listener: callable) -> None:
        """添加配置变更监听器"""
        if listener not in self._change_listeners:
            self._change_listeners.append(listener)

    def remove_change_listener(self, listener: callable) -> None:
        """移除配置变更监听器"""
        if listener in self._change_listeners:
            self._change_listeners.remove(listener)

    def _notify_listeners(self, event: ConfigChangeEvent) -> None:
        """通知所有监听器"""
        for listener in self._change_listeners:
            try:
                listener(event)
            except Exception as e:
                logger.error(f"配置变更监听器执行失败: {e}")

    def get_config_history(self, limit: int = 100) -> List[ConfigChangeEvent]:
        """获取配置变更历史"""
        return self._config_history[-limit:] if self._config_history else []

    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config.copy() if self._config else {}

    def get_dict(self) -> Dict[str, Any]:
        """获取配置字典 - 向后兼容方法"""
        return self.get_all_config()

    def validate_config(self) -> List[str]:
        """验证配置完整性"""
        errors = []

        # 检查必需的配置项
        required_keys = [
            "server.host",
            "server.port",
            "logging.level",
            "data.base_path"
        ]

        for key in required_keys:
            if not self.get(key):
                errors.append(f"缺少必需配置: {key}")

        # 检查数据路径
        try:
            base_path = Path(self.get("data.base_path", "./data"))
            if not base_path.exists():
                errors.append(f"数据基础路径不存在: {base_path}")
        except Exception as e:
            errors.append(f"数据路径配置错误: {e}")

        # 检查端口范围
        port = self.get("server.port")
        if port and (not isinstance(port, int) or port < 1 or port > 65535):
            errors.append(f"端口配置无效: {port}")

        return errors


# 全局配置实例
_unified_config = UnifiedConfigManager()


def get_unified_config() -> UnifiedConfigManager:
    """获取统一配置管理器实例"""
    return _unified_config


def get_config(key: str = None, default: Any = None) -> Union[UnifiedConfigManager, Any]:
    """
    获取配置
    
    Args:
        key: 配置键，如果为None则返回配置管理器实例
        default: 默认值
        
    Returns:
        配置值或配置管理器实例
    """
    if key is None:
        return _unified_config
    return _unified_config.get(key, default)


# 向后兼容的函数
def get_server_port() -> int:
    """获取服务器端口"""
    return get_config("server.port", 5002)


def get_server_host() -> str:
    """获取服务器主机"""
    return get_config("server.host", "0.0.0.0")


def is_production() -> bool:
    """是否为生产环境"""
    return get_config("system.environment", "development") == "production"


def get_production_config() -> Dict[str, Any]:
    """获取生产环境配置"""
    config_manager = get_config()
    if hasattr(config_manager, 'get_production_config'):
        return config_manager.get_production_config()

    # 回退配置
    return {
        "server": {
            "host": get_server_host(),
            "port": get_server_port(),
            "workers": 4,
            "debug": False,
            "reload": False
        },
        "security": {
            "secret_key": get_config("security.secret_key", "production-secret-key"),
            "rate_limit": "100/hour",
            "max_content_length": 16 * 1024 * 1024
        },
        "cache": {
            "max_size": 1000,
            "ttl": 3600
        },
        "logging": {
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        }
    }


# 数据路径管理函数（从 unified_data_config.py 迁移）
def get_equipment_data_path() -> Path:
    """获取设备数据路径"""
    config_manager = get_config()
    return config_manager.project_root / "data" / "01_raw" / "equipment"


def get_fault_cases_path() -> Path:
    """获取故障案例路径"""
    config_manager = get_config()
    return config_manager.project_root / "data" / "01_raw" / "fault_cases"


def get_knowledge_base_path() -> Path:
    """获取知识库路径"""
    config_manager = get_config()
    return config_manager.project_root / "knowledge_base"


def get_processed_data_path(data_type: DataType) -> Path:
    """获取处理后数据路径"""
    config_manager = get_config()
    base_path = config_manager.project_root / "data" / "02_processed"

    if data_type == DataType.STRUCTURED:
        return base_path / "structured"
    elif data_type == DataType.UNSTRUCTURED:
        return base_path / "unstructured"
    elif data_type == DataType.MULTIMODAL:
        return base_path / "multimodal"
    else:
        return base_path


def get_output_reports_path() -> Path:
    """获取输出报告路径"""
    config_manager = get_config()
    return config_manager.project_root / "data" / "04_production" / "reports"


def get_cache_path() -> Path:
    """获取缓存路径"""
    config_manager = get_config()
    return config_manager.project_root / "data" / "04_production" / "cached"
