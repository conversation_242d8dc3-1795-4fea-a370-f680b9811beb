2025-07-29 14:42:17 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-29 14:42:17 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-29 14:42:17 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-29 14:42:24 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-29 14:42:24 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-29 14:42:24 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-29 14:42:24 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-29 14:42:24 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-29 14:42:24 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-29 14:42:24 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, Flask-SocketIO, FastAPI, Uvicorn
2025-07-29 14:42:24 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-29 14:42:24 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV, PaddleOCR
2025-07-29 14:42:24 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-29 14:42:24 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-29 14:42:24 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-29 14:42:24 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-29 14:42:25 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-29 14:42:25 | INFO | data_processing.ocr_processor:<module>:25 | PaddleOCR可用
2025-07-29 14:42:26 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: 'UnifiedConfigManager' object has no attribute 'get_dict'
2025-07-29 14:42:26 | WARNING | core.system_initializer:_preload_critical_services:193 | ⚠️ 服务预加载失败: fault_analyzer
2025-07-29 14:42:26 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: 'UnifiedConfigManager' object has no attribute 'get_dict'
2025-07-29 14:42:26 | WARNING | core.system_initializer:_preload_critical_services:193 | ⚠️ 服务预加载失败: equipment_manager
2025-07-29 14:42:26 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-29 14:42:26 | INFO | core.system_initializer:print_startup_banner:201 | 
======================================================================
2025-07-29 14:42:26 | INFO | core.system_initializer:print_startup_banner:202 | 🚀 故障分析智能助手系统
2025-07-29 14:42:26 | INFO | core.system_initializer:print_startup_banner:203 | ======================================================================
2025-07-29 14:42:26 | INFO | core.system_initializer:print_startup_banner:204 | 📊 版本: 1.0.0
2025-07-29 14:42:26 | INFO | core.system_initializer:print_startup_banner:205 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-29 14:42:26 | INFO | core.system_initializer:print_startup_banner:206 | 🔧 运行模式: 调试模式
2025-07-29 14:42:26 | INFO | core.system_initializer:print_startup_banner:207 | 📁 工作目录: G:\my-dl-dmx
2025-07-29 14:42:26 | INFO | core.system_initializer:print_startup_banner:211 | 📡 WebSocket支持: ✅
2025-07-29 14:42:26 | INFO | core.system_initializer:print_startup_banner:212 | 🤖 AI分析: ✅
2025-07-29 14:42:26 | INFO | core.system_initializer:print_startup_banner:214 | 📊 数据处理: ✅
2025-07-29 14:42:26 | INFO | core.system_initializer:print_startup_banner:215 | 🖼️ 图像处理: ✅
2025-07-29 14:42:26 | INFO | core.system_initializer:print_startup_banner:216 | 📝 OCR识别: ✅
2025-07-29 14:42:26 | INFO | core.system_initializer:print_startup_banner:218 | ======================================================================
2025-07-29 14:42:26 | INFO | core.system_initializer:full_initialization:246 | ✅ 系统初始化完成
2025-07-29 14:42:26 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-29 14:42:26 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-29 14:42:26 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-29 14:42:26 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-29 14:42:26 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-29 14:42:26 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-29 14:42:26 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-29 14:42:26 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-29 14:42:26 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-29 14:42:26 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-29 14:42:26 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-29 14:42:26 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-29 14:42:26 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-29 14:42:26 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-29 14:42:26 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-29 14:42:26 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-29 14:42:26 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-29 14:42:26 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-29 14:42:26 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-29 14:42:26 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-29 14:42:26 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-29 14:42:27 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-29 14:42:54 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-29 14:42:54 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-29 14:42:54 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-29 14:42:56 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-29 14:42:56 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-29 14:42:56 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-29 14:43:04 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-29 14:43:04 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-29 14:43:04 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-29 14:43:04 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-29 14:43:04 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-29 14:43:04 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-29 14:43:04 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, Flask-SocketIO, FastAPI, Uvicorn
2025-07-29 14:43:04 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-29 14:43:04 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV, PaddleOCR
2025-07-29 14:43:04 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-29 14:43:04 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-29 14:43:04 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-29 14:43:04 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-29 14:43:04 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-29 14:43:04 | INFO | data_processing.ocr_processor:<module>:25 | PaddleOCR可用
2025-07-29 14:43:05 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: 'UnifiedConfigManager' object has no attribute 'get_dict'
2025-07-29 14:43:05 | WARNING | core.system_initializer:_preload_critical_services:193 | ⚠️ 服务预加载失败: fault_analyzer
2025-07-29 14:43:05 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: 'UnifiedConfigManager' object has no attribute 'get_dict'
2025-07-29 14:43:05 | WARNING | core.system_initializer:_preload_critical_services:193 | ⚠️ 服务预加载失败: equipment_manager
2025-07-29 14:43:05 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-29 14:43:05 | INFO | core.system_initializer:print_startup_banner:201 | 
======================================================================
2025-07-29 14:43:05 | INFO | core.system_initializer:print_startup_banner:202 | 🚀 故障分析智能助手系统
2025-07-29 14:43:05 | INFO | core.system_initializer:print_startup_banner:203 | ======================================================================
2025-07-29 14:43:05 | INFO | core.system_initializer:print_startup_banner:204 | 📊 版本: 1.0.0
2025-07-29 14:43:05 | INFO | core.system_initializer:print_startup_banner:205 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-29 14:43:05 | INFO | core.system_initializer:print_startup_banner:206 | 🔧 运行模式: 调试模式
2025-07-29 14:43:05 | INFO | core.system_initializer:print_startup_banner:207 | 📁 工作目录: G:\my-dl-dmx
2025-07-29 14:43:05 | INFO | core.system_initializer:print_startup_banner:211 | 📡 WebSocket支持: ✅
2025-07-29 14:43:05 | INFO | core.system_initializer:print_startup_banner:212 | 🤖 AI分析: ✅
2025-07-29 14:43:05 | INFO | core.system_initializer:print_startup_banner:214 | 📊 数据处理: ✅
2025-07-29 14:43:05 | INFO | core.system_initializer:print_startup_banner:215 | 🖼️ 图像处理: ✅
2025-07-29 14:43:05 | INFO | core.system_initializer:print_startup_banner:216 | 📝 OCR识别: ✅
2025-07-29 14:43:05 | INFO | core.system_initializer:print_startup_banner:218 | ======================================================================
2025-07-29 14:43:05 | INFO | core.system_initializer:full_initialization:246 | ✅ 系统初始化完成
2025-07-29 14:43:05 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-29 14:43:05 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-29 14:43:05 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-29 14:43:05 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-29 14:43:05 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-29 14:43:05 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-29 14:43:05 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-29 14:43:05 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-29 14:43:05 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-29 14:43:05 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-29 14:43:05 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-29 14:43:06 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-29 14:43:06 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-29 14:43:06 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-29 14:43:06 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-29 14:43:06 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-29 14:43:06 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-29 14:43:06 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-29 14:43:06 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-29 14:43:06 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-29 14:43:06 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-29 14:43:06 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-29 14:43:19 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-29 14:44:14 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-29 14:44:14 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-29 14:44:14 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-29 14:44:21 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-29 14:44:21 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-29 14:44:21 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-29 14:44:21 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-29 14:44:21 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-29 14:44:21 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-29 14:44:21 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, Flask-SocketIO, FastAPI, Uvicorn
2025-07-29 14:44:21 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-29 14:44:21 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV, PaddleOCR
2025-07-29 14:44:21 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-29 14:44:21 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-29 14:44:21 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-29 14:44:21 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-29 14:44:22 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-29 14:44:22 | INFO | data_processing.ocr_processor:<module>:25 | PaddleOCR可用
2025-07-29 14:44:23 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: 'UnifiedConfigManager' object has no attribute 'get_dict'
2025-07-29 14:44:23 | WARNING | core.system_initializer:_preload_critical_services:193 | ⚠️ 服务预加载失败: fault_analyzer
2025-07-29 14:44:23 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: 'UnifiedConfigManager' object has no attribute 'get_dict'
2025-07-29 14:44:23 | WARNING | core.system_initializer:_preload_critical_services:193 | ⚠️ 服务预加载失败: equipment_manager
2025-07-29 14:44:23 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-29 14:44:23 | INFO | core.system_initializer:print_startup_banner:201 | 
======================================================================
2025-07-29 14:44:23 | INFO | core.system_initializer:print_startup_banner:202 | 🚀 故障分析智能助手系统
2025-07-29 14:44:23 | INFO | core.system_initializer:print_startup_banner:203 | ======================================================================
2025-07-29 14:44:23 | INFO | core.system_initializer:print_startup_banner:204 | 📊 版本: 1.0.0
2025-07-29 14:44:23 | INFO | core.system_initializer:print_startup_banner:205 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-29 14:44:23 | INFO | core.system_initializer:print_startup_banner:206 | 🔧 运行模式: 调试模式
2025-07-29 14:44:23 | INFO | core.system_initializer:print_startup_banner:207 | 📁 工作目录: G:\my-dl-dmx
2025-07-29 14:44:23 | INFO | core.system_initializer:print_startup_banner:211 | 📡 WebSocket支持: ✅
2025-07-29 14:44:23 | INFO | core.system_initializer:print_startup_banner:212 | 🤖 AI分析: ✅
2025-07-29 14:44:23 | INFO | core.system_initializer:print_startup_banner:214 | 📊 数据处理: ✅
2025-07-29 14:44:23 | INFO | core.system_initializer:print_startup_banner:215 | 🖼️ 图像处理: ✅
2025-07-29 14:44:23 | INFO | core.system_initializer:print_startup_banner:216 | 📝 OCR识别: ✅
2025-07-29 14:44:23 | INFO | core.system_initializer:print_startup_banner:218 | ======================================================================
2025-07-29 14:44:23 | INFO | core.system_initializer:full_initialization:246 | ✅ 系统初始化完成
2025-07-29 14:44:23 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-29 14:44:23 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-29 14:44:23 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-29 14:44:23 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-29 14:44:23 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-29 14:44:23 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-29 14:44:23 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-29 14:44:23 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-29 14:44:23 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-29 14:44:23 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-29 14:44:23 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-29 14:44:23 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-29 14:44:23 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-29 14:44:23 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-29 14:44:23 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-29 14:44:23 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-29 14:44:23 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-29 14:44:23 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-29 14:44:23 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-29 14:44:23 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-29 14:44:23 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-29 14:44:24 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-29 14:44:51 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-29 14:44:51 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-29 14:44:51 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-29 14:44:53 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-29 14:44:53 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-29 14:44:53 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-29 14:45:02 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-29 14:45:02 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-29 14:45:02 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-29 14:45:02 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-29 14:45:02 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-29 14:45:02 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-29 14:45:02 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, Flask-SocketIO, FastAPI, Uvicorn
2025-07-29 14:45:02 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-29 14:45:02 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV, PaddleOCR
2025-07-29 14:45:02 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-29 14:45:02 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-29 14:45:02 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-29 14:45:02 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-29 14:45:02 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-29 14:45:03 | INFO | data_processing.ocr_processor:<module>:25 | PaddleOCR可用
2025-07-29 14:45:04 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: 'UnifiedConfigManager' object has no attribute 'get_dict'
2025-07-29 14:45:04 | WARNING | core.system_initializer:_preload_critical_services:193 | ⚠️ 服务预加载失败: fault_analyzer
2025-07-29 14:45:04 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: 'UnifiedConfigManager' object has no attribute 'get_dict'
2025-07-29 14:45:04 | WARNING | core.system_initializer:_preload_critical_services:193 | ⚠️ 服务预加载失败: equipment_manager
2025-07-29 14:45:04 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-29 14:45:04 | INFO | core.system_initializer:print_startup_banner:201 | 
======================================================================
2025-07-29 14:45:04 | INFO | core.system_initializer:print_startup_banner:202 | 🚀 故障分析智能助手系统
2025-07-29 14:45:04 | INFO | core.system_initializer:print_startup_banner:203 | ======================================================================
2025-07-29 14:45:04 | INFO | core.system_initializer:print_startup_banner:204 | 📊 版本: 1.0.0
2025-07-29 14:45:04 | INFO | core.system_initializer:print_startup_banner:205 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-29 14:45:04 | INFO | core.system_initializer:print_startup_banner:206 | 🔧 运行模式: 调试模式
2025-07-29 14:45:04 | INFO | core.system_initializer:print_startup_banner:207 | 📁 工作目录: G:\my-dl-dmx
2025-07-29 14:45:04 | INFO | core.system_initializer:print_startup_banner:211 | 📡 WebSocket支持: ✅
2025-07-29 14:45:04 | INFO | core.system_initializer:print_startup_banner:212 | 🤖 AI分析: ✅
2025-07-29 14:45:04 | INFO | core.system_initializer:print_startup_banner:214 | 📊 数据处理: ✅
2025-07-29 14:45:04 | INFO | core.system_initializer:print_startup_banner:215 | 🖼️ 图像处理: ✅
2025-07-29 14:45:04 | INFO | core.system_initializer:print_startup_banner:216 | 📝 OCR识别: ✅
2025-07-29 14:45:04 | INFO | core.system_initializer:print_startup_banner:218 | ======================================================================
2025-07-29 14:45:04 | INFO | core.system_initializer:full_initialization:246 | ✅ 系统初始化完成
2025-07-29 14:45:04 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-29 14:45:04 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-29 14:45:04 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-29 14:45:04 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-29 14:45:04 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-29 14:45:04 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-29 14:45:04 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-29 14:45:04 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-29 14:45:04 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-29 14:45:04 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-29 14:45:04 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-29 14:45:04 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-29 14:45:04 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-29 14:45:04 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-29 14:45:04 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-29 14:45:04 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-29 14:45:04 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-29 14:45:04 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-29 14:45:04 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-29 14:45:04 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-29 14:45:04 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-29 14:45:04 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-29 14:45:26 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-29 15:28:50 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-29 15:28:50 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-29 15:28:50 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-29 15:28:51 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ Flask-SocketIO 不可用: No module named 'flask_socketio'
2025-07-29 15:28:51 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础HTTP通信
2025-07-29 15:29:00 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ ChromaDB 不可用: No module named 'chromadb'
2025-07-29 15:29:00 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 内存存储
2025-07-29 15:29:00 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ OpenCV 不可用: No module named 'cv2'
2025-07-29 15:29:00 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础图像功能
2025-07-29 15:29:00 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddleocr'
2025-07-29 15:29:00 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-29 15:29:00 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-29 15:29:00 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-29 15:29:00 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-29 15:29:00 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-29 15:29:00 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-29 15:29:00 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-29 15:29:00 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, FastAPI, Uvicorn
2025-07-29 15:29:00 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: Flask-SocketIO
2025-07-29 15:29:00 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-29 15:29:00 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers
2025-07-29 15:29:00 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: ChromaDB, OpenCV, PaddleOCR
2025-07-29 15:29:00 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-29 15:29:00 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-29 15:29:00 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-29 15:29:00 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-29 15:29:01 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-29 15:29:01 | WARNING | data_processing.vector_processor:<module>:46 | Chroma未安装，将使用FAISS作为向量数据库
2025-07-29 15:29:01 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'cv2'
2025-07-29 15:29:01 | WARNING | core.system_initializer:_preload_critical_services:193 | ⚠️ 服务预加载失败: fault_analyzer
2025-07-29 15:29:01 | INFO | core.equipment_manager:load_equipment_database:49 | 加载了 5 个设备信息
2025-07-29 15:29:01 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-29 15:29:01 | INFO | core.system_initializer:print_startup_banner:201 | 
======================================================================
2025-07-29 15:29:01 | INFO | core.system_initializer:print_startup_banner:202 | 🚀 故障分析智能助手系统
2025-07-29 15:29:01 | INFO | core.system_initializer:print_startup_banner:203 | ======================================================================
2025-07-29 15:29:01 | INFO | core.system_initializer:print_startup_banner:204 | 📊 版本: 1.0.0
2025-07-29 15:29:01 | INFO | core.system_initializer:print_startup_banner:205 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-29 15:29:01 | INFO | core.system_initializer:print_startup_banner:206 | 🔧 运行模式: 调试模式
2025-07-29 15:29:01 | INFO | core.system_initializer:print_startup_banner:207 | 📁 工作目录: G:\my-dl-dmx
2025-07-29 15:29:01 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ OpenCV 不可用: No module named 'cv2'
2025-07-29 15:29:01 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础图像功能
2025-07-29 15:29:01 | INFO | core.system_initializer:print_startup_banner:211 | 📡 WebSocket支持: ❌
2025-07-29 15:29:01 | INFO | core.system_initializer:print_startup_banner:212 | 🤖 AI分析: ✅
2025-07-29 15:29:01 | INFO | core.system_initializer:print_startup_banner:214 | 📊 数据处理: ✅
2025-07-29 15:29:01 | INFO | core.system_initializer:print_startup_banner:215 | 🖼️ 图像处理: ❌
2025-07-29 15:29:01 | INFO | core.system_initializer:print_startup_banner:216 | 📝 OCR识别: ❌
2025-07-29 15:29:01 | INFO | core.system_initializer:print_startup_banner:218 | ======================================================================
2025-07-29 15:29:01 | INFO | core.system_initializer:full_initialization:246 | ✅ 系统初始化完成
2025-07-29 15:29:01 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-29 15:29:01 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-29 15:29:01 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-29 15:29:01 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-29 15:29:01 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-29 15:29:01 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-29 15:29:01 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-29 15:29:01 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-29 15:29:01 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-29 15:29:01 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-29 15:29:01 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-29 15:29:01 | WARNING | data_processing.modern_chroma_manager:<module>:17 | ChromaDB不可用: No module named 'chromadb'
2025-07-29 15:29:01 | ERROR | data_processing.modern_chroma_manager:__init__:34 | ChromaDB不可用，无法初始化
2025-07-29 15:29:01 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-29 15:29:01 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-29 15:29:01 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-29 15:29:01 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-29 15:29:01 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-29 15:29:01 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-29 15:29:01 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-29 15:29:01 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-29 15:29:20 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-29 15:29:20 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-29 15:29:20 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-29 15:29:20 | WARNING | __main__:start_flask_server:95 | SocketIO启动失败，使用标准Flask: run_simple() got an unexpected keyword argument 'allow_unsafe_werkzeug'
2025-07-29 15:29:22 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-29 15:29:22 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-29 15:29:22 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-29 15:29:23 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ Flask-SocketIO 不可用: No module named 'flask_socketio'
2025-07-29 15:29:23 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础HTTP通信
2025-07-29 15:29:32 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ ChromaDB 不可用: No module named 'chromadb'
2025-07-29 15:29:32 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 内存存储
2025-07-29 15:29:32 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ OpenCV 不可用: No module named 'cv2'
2025-07-29 15:29:32 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础图像功能
2025-07-29 15:29:32 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddleocr'
2025-07-29 15:29:32 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-29 15:29:32 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-29 15:29:32 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-29 15:29:32 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-29 15:29:32 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-29 15:29:32 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-29 15:29:32 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-29 15:29:32 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, FastAPI, Uvicorn
2025-07-29 15:29:32 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: Flask-SocketIO
2025-07-29 15:29:32 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-29 15:29:32 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers
2025-07-29 15:29:32 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: ChromaDB, OpenCV, PaddleOCR
2025-07-29 15:29:32 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-29 15:29:32 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-29 15:29:32 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-29 15:29:32 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-29 15:29:33 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-29 15:29:33 | WARNING | data_processing.vector_processor:<module>:46 | Chroma未安装，将使用FAISS作为向量数据库
2025-07-29 15:29:33 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'cv2'
2025-07-29 15:29:33 | WARNING | core.system_initializer:_preload_critical_services:193 | ⚠️ 服务预加载失败: fault_analyzer
2025-07-29 15:29:33 | INFO | core.equipment_manager:load_equipment_database:49 | 加载了 5 个设备信息
2025-07-29 15:29:33 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-29 15:29:33 | INFO | core.system_initializer:print_startup_banner:201 | 
======================================================================
2025-07-29 15:29:33 | INFO | core.system_initializer:print_startup_banner:202 | 🚀 故障分析智能助手系统
2025-07-29 15:29:33 | INFO | core.system_initializer:print_startup_banner:203 | ======================================================================
2025-07-29 15:29:33 | INFO | core.system_initializer:print_startup_banner:204 | 📊 版本: 1.0.0
2025-07-29 15:29:33 | INFO | core.system_initializer:print_startup_banner:205 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-29 15:29:33 | INFO | core.system_initializer:print_startup_banner:206 | 🔧 运行模式: 调试模式
2025-07-29 15:29:33 | INFO | core.system_initializer:print_startup_banner:207 | 📁 工作目录: G:\my-dl-dmx
2025-07-29 15:29:33 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ OpenCV 不可用: No module named 'cv2'
2025-07-29 15:29:33 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础图像功能
2025-07-29 15:29:33 | INFO | core.system_initializer:print_startup_banner:211 | 📡 WebSocket支持: ❌
2025-07-29 15:29:33 | INFO | core.system_initializer:print_startup_banner:212 | 🤖 AI分析: ✅
2025-07-29 15:29:33 | INFO | core.system_initializer:print_startup_banner:214 | 📊 数据处理: ✅
2025-07-29 15:29:33 | INFO | core.system_initializer:print_startup_banner:215 | 🖼️ 图像处理: ❌
2025-07-29 15:29:33 | INFO | core.system_initializer:print_startup_banner:216 | 📝 OCR识别: ❌
2025-07-29 15:29:33 | INFO | core.system_initializer:print_startup_banner:218 | ======================================================================
2025-07-29 15:29:33 | INFO | core.system_initializer:full_initialization:246 | ✅ 系统初始化完成
2025-07-29 15:29:33 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-29 15:29:33 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-29 15:29:33 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-29 15:29:33 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-29 15:29:33 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-29 15:29:33 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-29 15:29:33 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-29 15:29:33 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-29 15:29:33 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-29 15:29:33 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-29 15:29:33 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-29 15:29:34 | WARNING | data_processing.modern_chroma_manager:<module>:17 | ChromaDB不可用: No module named 'chromadb'
2025-07-29 15:29:34 | ERROR | data_processing.modern_chroma_manager:__init__:34 | ChromaDB不可用，无法初始化
2025-07-29 15:29:34 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-29 15:29:34 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-29 15:29:34 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-29 15:29:34 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-29 15:29:34 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-29 15:29:34 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-29 15:29:34 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-29 15:29:34 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-29 15:29:52 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-29 15:39:04 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-29 15:39:04 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-29 15:39:04 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-29 15:39:05 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ Flask-SocketIO 不可用: No module named 'flask_socketio'
2025-07-29 15:39:05 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础HTTP通信
2025-07-29 15:39:12 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddleocr'
2025-07-29 15:39:12 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-29 15:39:12 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-29 15:39:12 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-29 15:39:12 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-29 15:39:12 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-29 15:39:12 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-29 15:39:12 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-29 15:39:12 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, FastAPI, Uvicorn
2025-07-29 15:39:12 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: Flask-SocketIO
2025-07-29 15:39:12 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-29 15:39:12 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV
2025-07-29 15:39:12 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: PaddleOCR
2025-07-29 15:39:12 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-29 15:39:12 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-29 15:39:12 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-29 15:39:12 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-29 15:39:13 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-29 15:39:13 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'easyocr'
2025-07-29 15:39:13 | WARNING | core.system_initializer:_preload_critical_services:193 | ⚠️ 服务预加载失败: fault_analyzer
2025-07-29 15:39:13 | INFO | core.equipment_manager:load_equipment_database:49 | 加载了 5 个设备信息
2025-07-29 15:39:13 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-29 15:39:13 | INFO | core.system_initializer:print_startup_banner:201 | 
======================================================================
2025-07-29 15:39:13 | INFO | core.system_initializer:print_startup_banner:202 | 🚀 故障分析智能助手系统
2025-07-29 15:39:13 | INFO | core.system_initializer:print_startup_banner:203 | ======================================================================
2025-07-29 15:39:13 | INFO | core.system_initializer:print_startup_banner:204 | 📊 版本: 1.0.0
2025-07-29 15:39:13 | INFO | core.system_initializer:print_startup_banner:205 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-29 15:39:13 | INFO | core.system_initializer:print_startup_banner:206 | 🔧 运行模式: 调试模式
2025-07-29 15:39:13 | INFO | core.system_initializer:print_startup_banner:207 | 📁 工作目录: G:\my-dl-dmx
2025-07-29 15:39:13 | INFO | core.system_initializer:print_startup_banner:211 | 📡 WebSocket支持: ❌
2025-07-29 15:39:13 | INFO | core.system_initializer:print_startup_banner:212 | 🤖 AI分析: ✅
2025-07-29 15:39:13 | INFO | core.system_initializer:print_startup_banner:214 | 📊 数据处理: ✅
2025-07-29 15:39:13 | INFO | core.system_initializer:print_startup_banner:215 | 🖼️ 图像处理: ✅
2025-07-29 15:39:13 | INFO | core.system_initializer:print_startup_banner:216 | 📝 OCR识别: ❌
2025-07-29 15:39:13 | INFO | core.system_initializer:print_startup_banner:218 | ======================================================================
2025-07-29 15:39:13 | INFO | core.system_initializer:full_initialization:246 | ✅ 系统初始化完成
2025-07-29 15:39:13 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-29 15:39:13 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-29 15:39:13 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-29 15:39:13 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-29 15:39:13 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-29 15:39:13 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-29 15:39:13 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-29 15:39:13 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-29 15:39:13 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-29 15:39:13 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-29 15:39:13 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-29 15:39:13 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-29 15:39:13 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-29 15:39:14 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-29 15:39:14 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-29 15:39:14 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-29 15:39:14 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-29 15:39:14 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-29 15:39:14 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-29 15:39:14 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-29 15:39:14 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-29 15:39:35 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-29 15:39:35 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-29 15:39:35 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-29 15:39:35 | WARNING | __main__:start_flask_server:95 | SocketIO启动失败，使用标准Flask: run_simple() got an unexpected keyword argument 'allow_unsafe_werkzeug'
2025-07-29 15:39:36 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-29 15:39:36 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-29 15:39:36 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-29 15:39:37 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ Flask-SocketIO 不可用: No module named 'flask_socketio'
2025-07-29 15:39:37 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础HTTP通信
2025-07-29 15:39:42 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddleocr'
2025-07-29 15:39:42 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-29 15:39:42 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-29 15:39:42 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-29 15:39:42 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-29 15:39:42 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-29 15:39:42 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-29 15:39:42 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-29 15:39:42 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, FastAPI, Uvicorn
2025-07-29 15:39:42 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: Flask-SocketIO
2025-07-29 15:39:42 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-29 15:39:42 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV
2025-07-29 15:39:42 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: PaddleOCR
2025-07-29 15:39:42 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-29 15:39:42 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-29 15:39:42 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-29 15:39:42 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-29 15:39:43 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-29 15:39:43 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'easyocr'
2025-07-29 15:39:43 | WARNING | core.system_initializer:_preload_critical_services:193 | ⚠️ 服务预加载失败: fault_analyzer
2025-07-29 15:39:43 | INFO | core.equipment_manager:load_equipment_database:49 | 加载了 5 个设备信息
2025-07-29 15:39:43 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-29 15:39:43 | INFO | core.system_initializer:print_startup_banner:201 | 
======================================================================
2025-07-29 15:39:43 | INFO | core.system_initializer:print_startup_banner:202 | 🚀 故障分析智能助手系统
2025-07-29 15:39:43 | INFO | core.system_initializer:print_startup_banner:203 | ======================================================================
2025-07-29 15:39:43 | INFO | core.system_initializer:print_startup_banner:204 | 📊 版本: 1.0.0
2025-07-29 15:39:43 | INFO | core.system_initializer:print_startup_banner:205 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-29 15:39:43 | INFO | core.system_initializer:print_startup_banner:206 | 🔧 运行模式: 调试模式
2025-07-29 15:39:43 | INFO | core.system_initializer:print_startup_banner:207 | 📁 工作目录: G:\my-dl-dmx
2025-07-29 15:39:43 | INFO | core.system_initializer:print_startup_banner:211 | 📡 WebSocket支持: ❌
2025-07-29 15:39:43 | INFO | core.system_initializer:print_startup_banner:212 | 🤖 AI分析: ✅
2025-07-29 15:39:43 | INFO | core.system_initializer:print_startup_banner:214 | 📊 数据处理: ✅
2025-07-29 15:39:43 | INFO | core.system_initializer:print_startup_banner:215 | 🖼️ 图像处理: ✅
2025-07-29 15:39:43 | INFO | core.system_initializer:print_startup_banner:216 | 📝 OCR识别: ❌
2025-07-29 15:39:43 | INFO | core.system_initializer:print_startup_banner:218 | ======================================================================
2025-07-29 15:39:43 | INFO | core.system_initializer:full_initialization:246 | ✅ 系统初始化完成
2025-07-29 15:39:43 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-29 15:39:43 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-29 15:39:43 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-29 15:39:43 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-29 15:39:43 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-29 15:39:43 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-29 15:39:43 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-29 15:39:43 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-29 15:39:43 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-29 15:39:43 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-29 15:39:43 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-29 15:39:43 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-29 15:39:43 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-29 15:39:43 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-29 15:39:43 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-29 15:39:43 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-29 15:39:43 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-29 15:39:43 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-29 15:39:43 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-29 15:39:43 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-29 15:39:43 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-29 15:40:03 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-29 15:40:03 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-29 15:40:03 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-29 15:40:03 | WARNING | __main__:start_flask_server:95 | SocketIO启动失败，使用标准Flask: run_simple() got an unexpected keyword argument 'allow_unsafe_werkzeug'
2025-07-29 15:41:07 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-29 15:41:10 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-29 15:41:10 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-29 15:41:10 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-29 15:41:10 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ Flask-SocketIO 不可用: No module named 'flask_socketio'
2025-07-29 15:41:10 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础HTTP通信
2025-07-29 15:41:16 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddleocr'
2025-07-29 15:41:16 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-29 15:41:16 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-29 15:41:16 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-29 15:41:16 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-29 15:41:16 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-29 15:41:16 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-29 15:41:16 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-29 15:41:16 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, FastAPI, Uvicorn
2025-07-29 15:41:16 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: Flask-SocketIO
2025-07-29 15:41:16 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-29 15:41:16 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV
2025-07-29 15:41:16 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: PaddleOCR
2025-07-29 15:41:16 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-29 15:41:16 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-29 15:41:16 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-29 15:41:16 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-29 15:41:16 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-29 15:41:16 | INFO | data_processing.ocr_processor:<module>:25 | EasyOCR未安装，将使用Tesseract
2025-07-29 15:41:16 | INFO | data_processing.ocr_processor:<module>:39 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-29 15:41:17 | INFO | data_processing.ocr_processor:_init_ocr_engines:108 | EasyOCR不可用，跳过初始化
2025-07-29 15:41:17 | INFO | data_processing.ocr_processor:_init_ocr_engines:112 | 所有OCR引擎都不可用，将使用基础文本处理功能
2025-07-29 15:41:17 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-29 15:41:17 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 'NoneType' object has no attribute 'get'
2025-07-29 15:41:17 | INFO | core.equipment_manager:load_equipment_database:49 | 加载了 5 个设备信息
2025-07-29 15:41:17 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-29 15:41:17 | INFO | core.system_initializer:print_startup_banner:201 | 
======================================================================
2025-07-29 15:41:17 | INFO | core.system_initializer:print_startup_banner:202 | 🚀 故障分析智能助手系统
2025-07-29 15:41:17 | INFO | core.system_initializer:print_startup_banner:203 | ======================================================================
2025-07-29 15:41:17 | INFO | core.system_initializer:print_startup_banner:204 | 📊 版本: 1.0.0
2025-07-29 15:41:17 | INFO | core.system_initializer:print_startup_banner:205 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-29 15:41:17 | INFO | core.system_initializer:print_startup_banner:206 | 🔧 运行模式: 调试模式
2025-07-29 15:41:17 | INFO | core.system_initializer:print_startup_banner:207 | 📁 工作目录: G:\my-dl-dmx
2025-07-29 15:41:17 | INFO | core.system_initializer:print_startup_banner:211 | 📡 WebSocket支持: ❌
2025-07-29 15:41:17 | INFO | core.system_initializer:print_startup_banner:212 | 🤖 AI分析: ✅
2025-07-29 15:41:17 | INFO | core.system_initializer:print_startup_banner:214 | 📊 数据处理: ✅
2025-07-29 15:41:17 | INFO | core.system_initializer:print_startup_banner:215 | 🖼️ 图像处理: ✅
2025-07-29 15:41:17 | INFO | core.system_initializer:print_startup_banner:216 | 📝 OCR识别: ❌
2025-07-29 15:41:17 | INFO | core.system_initializer:print_startup_banner:218 | ======================================================================
2025-07-29 15:41:17 | INFO | core.system_initializer:full_initialization:246 | ✅ 系统初始化完成
2025-07-29 15:41:17 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-29 15:41:17 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-29 15:41:17 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-29 15:41:17 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-29 15:41:17 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-29 15:41:17 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-29 15:41:17 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-29 15:41:17 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-29 15:41:17 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-29 15:41:17 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-29 15:41:17 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-29 15:41:18 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-29 15:41:18 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-29 15:41:18 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-29 15:41:18 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-29 15:41:18 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-29 15:41:18 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-29 15:41:18 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-29 15:41:18 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-29 15:41:18 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-29 15:41:18 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-29 15:41:18 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-29 15:41:39 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-29 15:41:39 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-29 15:41:39 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-29 15:41:39 | WARNING | __main__:start_flask_server:95 | SocketIO启动失败，使用标准Flask: run_simple() got an unexpected keyword argument 'allow_unsafe_werkzeug'
2025-07-29 15:42:15 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-29 15:42:19 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-29 15:42:19 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-29 15:42:19 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-29 15:42:19 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ Flask-SocketIO 不可用: No module named 'flask_socketio'
2025-07-29 15:42:19 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础HTTP通信
2025-07-29 15:42:25 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddleocr'
2025-07-29 15:42:25 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-29 15:42:25 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-29 15:42:25 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-29 15:42:25 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-29 15:42:25 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-29 15:42:25 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-29 15:42:25 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-29 15:42:25 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, FastAPI, Uvicorn
2025-07-29 15:42:25 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: Flask-SocketIO
2025-07-29 15:42:25 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-29 15:42:25 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV
2025-07-29 15:42:25 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: PaddleOCR
2025-07-29 15:42:25 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-29 15:42:25 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-29 15:42:25 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-29 15:42:25 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-29 15:42:26 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-29 15:42:26 | INFO | data_processing.ocr_processor:<module>:25 | EasyOCR未安装，将使用Tesseract
2025-07-29 15:42:26 | INFO | data_processing.ocr_processor:<module>:39 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-29 15:42:26 | INFO | data_processing.ocr_processor:_init_ocr_engines:108 | EasyOCR不可用，跳过初始化
2025-07-29 15:42:26 | INFO | data_processing.ocr_processor:_init_ocr_engines:112 | 所有OCR引擎都不可用，将使用基础文本处理功能
2025-07-29 15:42:26 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-29 15:42:26 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 'NoneType' object has no attribute 'get'
2025-07-29 15:42:26 | INFO | core.equipment_manager:load_equipment_database:49 | 加载了 5 个设备信息
2025-07-29 15:42:26 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-29 15:42:26 | INFO | core.system_initializer:print_startup_banner:201 | 
======================================================================
2025-07-29 15:42:26 | INFO | core.system_initializer:print_startup_banner:202 | 🚀 故障分析智能助手系统
2025-07-29 15:42:26 | INFO | core.system_initializer:print_startup_banner:203 | ======================================================================
2025-07-29 15:42:26 | INFO | core.system_initializer:print_startup_banner:204 | 📊 版本: 1.0.0
2025-07-29 15:42:26 | INFO | core.system_initializer:print_startup_banner:205 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-29 15:42:26 | INFO | core.system_initializer:print_startup_banner:206 | 🔧 运行模式: 调试模式
2025-07-29 15:42:26 | INFO | core.system_initializer:print_startup_banner:207 | 📁 工作目录: G:\my-dl-dmx
2025-07-29 15:42:26 | INFO | core.system_initializer:print_startup_banner:211 | 📡 WebSocket支持: ❌
2025-07-29 15:42:26 | INFO | core.system_initializer:print_startup_banner:212 | 🤖 AI分析: ✅
2025-07-29 15:42:26 | INFO | core.system_initializer:print_startup_banner:214 | 📊 数据处理: ✅
2025-07-29 15:42:26 | INFO | core.system_initializer:print_startup_banner:215 | 🖼️ 图像处理: ✅
2025-07-29 15:42:26 | INFO | core.system_initializer:print_startup_banner:216 | 📝 OCR识别: ❌
2025-07-29 15:42:26 | INFO | core.system_initializer:print_startup_banner:218 | ======================================================================
2025-07-29 15:42:26 | INFO | core.system_initializer:full_initialization:246 | ✅ 系统初始化完成
2025-07-29 15:42:26 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-29 15:42:26 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-29 15:42:26 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-29 15:42:26 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-29 15:42:26 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-29 15:42:26 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-29 15:42:26 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-29 15:42:26 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-29 15:42:26 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-29 15:42:26 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-29 15:42:26 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-29 15:42:27 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-29 15:42:27 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-29 15:42:27 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-29 15:42:27 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-29 15:42:27 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-29 15:42:27 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-29 15:42:27 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-29 15:42:27 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-29 15:42:27 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-29 15:42:27 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-29 15:42:27 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-29 15:42:48 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-29 15:42:48 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-29 15:42:48 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-29 15:42:48 | WARNING | __main__:start_flask_server:95 | SocketIO启动失败，使用标准Flask: run_simple() got an unexpected keyword argument 'allow_unsafe_werkzeug'
2025-07-29 15:43:12 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-29 15:43:15 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-29 15:43:15 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-29 15:43:15 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-29 15:43:15 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ Flask-SocketIO 不可用: No module named 'flask_socketio'
2025-07-29 15:43:15 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础HTTP通信
2025-07-29 15:43:21 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddleocr'
2025-07-29 15:43:21 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-29 15:43:21 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-29 15:43:21 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-29 15:43:21 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-29 15:43:21 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-29 15:43:21 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-29 15:43:21 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-29 15:43:21 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, FastAPI, Uvicorn
2025-07-29 15:43:21 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: Flask-SocketIO
2025-07-29 15:43:21 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-29 15:43:21 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV
2025-07-29 15:43:21 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: PaddleOCR
2025-07-29 15:43:21 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-29 15:43:21 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-29 15:43:21 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-29 15:43:21 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-29 15:43:22 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-29 15:43:22 | INFO | data_processing.ocr_processor:<module>:25 | EasyOCR未安装，将使用Tesseract
2025-07-29 15:43:22 | INFO | data_processing.ocr_processor:<module>:39 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-29 15:43:22 | INFO | data_processing.ocr_processor:_init_ocr_engines:108 | EasyOCR不可用，跳过初始化
2025-07-29 15:43:22 | INFO | data_processing.ocr_processor:_init_ocr_engines:112 | 所有OCR引擎都不可用，将使用基础文本处理功能
2025-07-29 15:43:22 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-29 15:43:22 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 'NoneType' object has no attribute 'get'
2025-07-29 15:43:22 | INFO | core.equipment_manager:load_equipment_database:49 | 加载了 5 个设备信息
2025-07-29 15:43:22 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-29 15:43:22 | INFO | core.system_initializer:print_startup_banner:201 | 
======================================================================
2025-07-29 15:43:22 | INFO | core.system_initializer:print_startup_banner:202 | 🚀 故障分析智能助手系统
2025-07-29 15:43:22 | INFO | core.system_initializer:print_startup_banner:203 | ======================================================================
2025-07-29 15:43:22 | INFO | core.system_initializer:print_startup_banner:204 | 📊 版本: 1.0.0
2025-07-29 15:43:22 | INFO | core.system_initializer:print_startup_banner:205 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-29 15:43:22 | INFO | core.system_initializer:print_startup_banner:206 | 🔧 运行模式: 调试模式
2025-07-29 15:43:22 | INFO | core.system_initializer:print_startup_banner:207 | 📁 工作目录: G:\my-dl-dmx
2025-07-29 15:43:22 | INFO | core.system_initializer:print_startup_banner:211 | 📡 WebSocket支持: ❌
2025-07-29 15:43:22 | INFO | core.system_initializer:print_startup_banner:212 | 🤖 AI分析: ✅
2025-07-29 15:43:22 | INFO | core.system_initializer:print_startup_banner:214 | 📊 数据处理: ✅
2025-07-29 15:43:22 | INFO | core.system_initializer:print_startup_banner:215 | 🖼️ 图像处理: ✅
2025-07-29 15:43:22 | INFO | core.system_initializer:print_startup_banner:216 | 📝 OCR识别: ❌
2025-07-29 15:43:22 | INFO | core.system_initializer:print_startup_banner:218 | ======================================================================
2025-07-29 15:43:22 | INFO | core.system_initializer:full_initialization:246 | ✅ 系统初始化完成
2025-07-29 15:43:22 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-29 15:43:22 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-29 15:43:22 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-29 15:43:22 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-29 15:43:22 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-29 15:43:22 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-29 15:43:22 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-29 15:43:22 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-29 15:43:22 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-29 15:43:22 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-29 15:43:22 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-29 15:43:23 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-29 15:43:23 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-29 15:43:23 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-29 15:43:23 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-29 15:43:23 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-29 15:43:23 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-29 15:43:23 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-29 15:43:23 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-29 15:43:23 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-29 15:43:23 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-29 15:43:23 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-29 15:43:44 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-29 15:43:44 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-29 15:43:44 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-29 15:43:44 | WARNING | __main__:start_flask_server:95 | SocketIO启动失败，使用标准Flask: run_simple() got an unexpected keyword argument 'allow_unsafe_werkzeug'
2025-07-29 15:44:05 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-29 15:44:08 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-29 15:44:08 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-29 15:44:08 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-29 15:44:09 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ Flask-SocketIO 不可用: No module named 'flask_socketio'
2025-07-29 15:44:09 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础HTTP通信
2025-07-29 15:44:15 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddleocr'
2025-07-29 15:44:15 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-29 15:44:15 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-29 15:44:15 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-29 15:44:15 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-29 15:44:15 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-29 15:44:15 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-29 15:44:15 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-29 15:44:15 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, FastAPI, Uvicorn
2025-07-29 15:44:15 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: Flask-SocketIO
2025-07-29 15:44:15 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-29 15:44:15 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV
2025-07-29 15:44:15 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: PaddleOCR
2025-07-29 15:44:15 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-29 15:44:15 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-29 15:44:15 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-29 15:44:15 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-29 15:44:15 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-29 15:44:15 | INFO | data_processing.ocr_processor:<module>:25 | EasyOCR未安装，将使用Tesseract
2025-07-29 15:44:15 | INFO | data_processing.ocr_processor:<module>:39 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-29 15:44:16 | INFO | data_processing.ocr_processor:_init_ocr_engines:108 | EasyOCR不可用，跳过初始化
2025-07-29 15:44:16 | INFO | data_processing.ocr_processor:_init_ocr_engines:112 | 所有OCR引擎都不可用，将使用基础文本处理功能
2025-07-29 15:44:16 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-29 15:44:16 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 'NoneType' object has no attribute 'get'
2025-07-29 15:44:16 | INFO | core.equipment_manager:load_equipment_database:49 | 加载了 5 个设备信息
2025-07-29 15:44:16 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-29 15:44:16 | INFO | core.system_initializer:print_startup_banner:201 | 
======================================================================
2025-07-29 15:44:16 | INFO | core.system_initializer:print_startup_banner:202 | 🚀 故障分析智能助手系统
2025-07-29 15:44:16 | INFO | core.system_initializer:print_startup_banner:203 | ======================================================================
2025-07-29 15:44:16 | INFO | core.system_initializer:print_startup_banner:204 | 📊 版本: 1.0.0
2025-07-29 15:44:16 | INFO | core.system_initializer:print_startup_banner:205 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-29 15:44:16 | INFO | core.system_initializer:print_startup_banner:206 | 🔧 运行模式: 调试模式
2025-07-29 15:44:16 | INFO | core.system_initializer:print_startup_banner:207 | 📁 工作目录: G:\my-dl-dmx
2025-07-29 15:44:16 | INFO | core.system_initializer:print_startup_banner:211 | 📡 WebSocket支持: ❌
2025-07-29 15:44:16 | INFO | core.system_initializer:print_startup_banner:212 | 🤖 AI分析: ✅
2025-07-29 15:44:16 | INFO | core.system_initializer:print_startup_banner:214 | 📊 数据处理: ✅
2025-07-29 15:44:16 | INFO | core.system_initializer:print_startup_banner:215 | 🖼️ 图像处理: ✅
2025-07-29 15:44:16 | INFO | core.system_initializer:print_startup_banner:216 | 📝 OCR识别: ❌
2025-07-29 15:44:16 | INFO | core.system_initializer:print_startup_banner:218 | ======================================================================
2025-07-29 15:44:16 | INFO | core.system_initializer:full_initialization:246 | ✅ 系统初始化完成
2025-07-29 15:44:16 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-29 15:44:16 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-29 15:44:16 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-29 15:44:16 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-29 15:44:16 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-29 15:44:16 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-29 15:44:16 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-29 15:44:16 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-29 15:44:16 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-29 15:44:16 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-29 15:44:16 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-29 15:44:16 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-29 15:44:16 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-29 15:44:16 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-29 15:44:16 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-29 15:44:17 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-29 15:44:17 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-29 15:44:17 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-29 15:44:17 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-29 15:44:17 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-29 15:44:17 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-29 15:44:17 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-29 15:44:37 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-29 15:44:37 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-29 15:44:37 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-29 15:44:37 | WARNING | __main__:start_flask_server:95 | SocketIO启动失败，使用标准Flask: run_simple() got an unexpected keyword argument 'allow_unsafe_werkzeug'
2025-07-29 15:45:10 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-29 15:45:13 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-29 15:45:13 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-29 15:45:13 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-29 15:45:13 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ Flask-SocketIO 不可用: No module named 'flask_socketio'
2025-07-29 15:45:13 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础HTTP通信
2025-07-29 15:45:19 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddleocr'
2025-07-29 15:45:19 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-29 15:45:19 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-29 15:45:19 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-29 15:45:19 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-29 15:45:19 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-29 15:45:19 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-29 15:45:19 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-29 15:45:19 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, FastAPI, Uvicorn
2025-07-29 15:45:19 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: Flask-SocketIO
2025-07-29 15:45:19 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-29 15:45:19 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV
2025-07-29 15:45:19 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: PaddleOCR
2025-07-29 15:45:19 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-29 15:45:19 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-29 15:45:19 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-29 15:45:19 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-29 15:45:19 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-29 15:45:19 | INFO | data_processing.ocr_processor:<module>:25 | EasyOCR未安装，将使用Tesseract
2025-07-29 15:45:19 | INFO | data_processing.ocr_processor:<module>:39 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-29 15:45:20 | INFO | data_processing.ocr_processor:_init_ocr_engines:108 | EasyOCR不可用，跳过初始化
2025-07-29 15:45:20 | INFO | data_processing.ocr_processor:_init_ocr_engines:112 | 所有OCR引擎都不可用，将使用基础文本处理功能
2025-07-29 15:45:20 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-29 15:45:20 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 'NoneType' object has no attribute 'get'
2025-07-29 15:45:20 | INFO | core.equipment_manager:load_equipment_database:49 | 加载了 5 个设备信息
2025-07-29 15:45:20 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-29 15:45:20 | INFO | core.system_initializer:print_startup_banner:201 | 
======================================================================
2025-07-29 15:45:20 | INFO | core.system_initializer:print_startup_banner:202 | 🚀 故障分析智能助手系统
2025-07-29 15:45:20 | INFO | core.system_initializer:print_startup_banner:203 | ======================================================================
2025-07-29 15:45:20 | INFO | core.system_initializer:print_startup_banner:204 | 📊 版本: 1.0.0
2025-07-29 15:45:20 | INFO | core.system_initializer:print_startup_banner:205 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-29 15:45:20 | INFO | core.system_initializer:print_startup_banner:206 | 🔧 运行模式: 调试模式
2025-07-29 15:45:20 | INFO | core.system_initializer:print_startup_banner:207 | 📁 工作目录: G:\my-dl-dmx
2025-07-29 15:45:20 | INFO | core.system_initializer:print_startup_banner:211 | 📡 WebSocket支持: ❌
2025-07-29 15:45:20 | INFO | core.system_initializer:print_startup_banner:212 | 🤖 AI分析: ✅
2025-07-29 15:45:20 | INFO | core.system_initializer:print_startup_banner:214 | 📊 数据处理: ✅
2025-07-29 15:45:20 | INFO | core.system_initializer:print_startup_banner:215 | 🖼️ 图像处理: ✅
2025-07-29 15:45:20 | INFO | core.system_initializer:print_startup_banner:216 | 📝 OCR识别: ❌
2025-07-29 15:45:20 | INFO | core.system_initializer:print_startup_banner:218 | ======================================================================
2025-07-29 15:45:20 | INFO | core.system_initializer:full_initialization:246 | ✅ 系统初始化完成
2025-07-29 15:45:20 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-29 15:45:20 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-29 15:45:20 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-29 15:45:20 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-29 15:45:20 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-29 15:45:20 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-29 15:45:20 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-29 15:45:20 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-29 15:45:20 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-29 15:45:20 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-29 15:45:20 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-29 15:45:20 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-29 15:45:20 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-29 15:45:20 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-29 15:45:20 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-29 15:45:20 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-29 15:45:20 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-29 15:45:20 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-29 15:45:20 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-29 15:45:20 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-29 15:45:20 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-29 15:45:21 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-29 15:45:41 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-29 15:45:41 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-29 15:45:41 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-29 15:45:41 | WARNING | __main__:start_flask_server:95 | SocketIO启动失败，使用标准Flask: run_simple() got an unexpected keyword argument 'allow_unsafe_werkzeug'
2025-07-29 15:45:47 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-29 15:45:50 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-29 15:45:50 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-29 15:45:50 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-29 15:45:51 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ Flask-SocketIO 不可用: No module named 'flask_socketio'
2025-07-29 15:45:51 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础HTTP通信
2025-07-29 15:45:57 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddleocr'
2025-07-29 15:45:57 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-29 15:45:57 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-29 15:45:57 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-29 15:45:57 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-29 15:45:57 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-29 15:45:57 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-29 15:45:57 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-29 15:45:57 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, FastAPI, Uvicorn
2025-07-29 15:45:57 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: Flask-SocketIO
2025-07-29 15:45:57 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-29 15:45:57 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV
2025-07-29 15:45:57 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: PaddleOCR
2025-07-29 15:45:57 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-29 15:45:57 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-29 15:45:57 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-29 15:45:57 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-29 15:45:57 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-29 15:45:57 | INFO | data_processing.ocr_processor:<module>:25 | EasyOCR未安装，将使用Tesseract
2025-07-29 15:45:57 | INFO | data_processing.ocr_processor:<module>:39 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-29 15:45:58 | INFO | data_processing.ocr_processor:_init_ocr_engines:108 | EasyOCR不可用，跳过初始化
2025-07-29 15:45:58 | INFO | data_processing.ocr_processor:_init_ocr_engines:112 | 所有OCR引擎都不可用，将使用基础文本处理功能
2025-07-29 15:45:58 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-29 15:45:58 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 'NoneType' object has no attribute 'get'
2025-07-29 15:45:58 | INFO | core.equipment_manager:load_equipment_database:49 | 加载了 5 个设备信息
2025-07-29 15:45:58 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-29 15:45:58 | INFO | core.system_initializer:print_startup_banner:201 | 
======================================================================
2025-07-29 15:45:58 | INFO | core.system_initializer:print_startup_banner:202 | 🚀 故障分析智能助手系统
2025-07-29 15:45:58 | INFO | core.system_initializer:print_startup_banner:203 | ======================================================================
2025-07-29 15:45:58 | INFO | core.system_initializer:print_startup_banner:204 | 📊 版本: 1.0.0
2025-07-29 15:45:58 | INFO | core.system_initializer:print_startup_banner:205 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-29 15:45:58 | INFO | core.system_initializer:print_startup_banner:206 | 🔧 运行模式: 调试模式
2025-07-29 15:45:58 | INFO | core.system_initializer:print_startup_banner:207 | 📁 工作目录: G:\my-dl-dmx
2025-07-29 15:45:58 | INFO | core.system_initializer:print_startup_banner:211 | 📡 WebSocket支持: ❌
2025-07-29 15:45:58 | INFO | core.system_initializer:print_startup_banner:212 | 🤖 AI分析: ✅
2025-07-29 15:45:58 | INFO | core.system_initializer:print_startup_banner:214 | 📊 数据处理: ✅
2025-07-29 15:45:58 | INFO | core.system_initializer:print_startup_banner:215 | 🖼️ 图像处理: ✅
2025-07-29 15:45:58 | INFO | core.system_initializer:print_startup_banner:216 | 📝 OCR识别: ❌
2025-07-29 15:45:58 | INFO | core.system_initializer:print_startup_banner:218 | ======================================================================
2025-07-29 15:45:58 | INFO | core.system_initializer:full_initialization:246 | ✅ 系统初始化完成
2025-07-29 15:45:58 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-29 15:45:58 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-29 15:45:58 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-29 15:45:58 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-29 15:45:58 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-29 15:45:58 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-29 15:45:58 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-29 15:45:58 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-29 15:45:58 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-29 15:45:58 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-29 15:45:58 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-29 15:45:58 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-29 15:45:58 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-29 15:45:58 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-29 15:45:58 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-29 15:45:58 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-29 15:45:58 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-29 15:45:58 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-29 15:45:58 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-29 15:45:58 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-29 15:45:58 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-29 15:45:58 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-29 15:46:19 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-29 15:46:19 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-29 15:46:19 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-29 15:46:19 | WARNING | __main__:start_flask_server:95 | SocketIO启动失败，使用标准Flask: run_simple() got an unexpected keyword argument 'allow_unsafe_werkzeug'
2025-07-29 15:46:42 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-29 15:46:45 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-30 09:18:45 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-30 09:18:45 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-30 09:18:45 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-30 09:18:46 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ Flask-SocketIO 不可用: No module named 'flask_socketio'
2025-07-30 09:18:46 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础HTTP通信
2025-07-30 09:18:58 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddleocr'
2025-07-30 09:18:58 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-30 09:18:58 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-30 09:18:58 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-30 09:18:58 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-30 09:18:58 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-30 09:18:58 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-30 09:18:58 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-30 09:18:58 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, FastAPI, Uvicorn
2025-07-30 09:18:58 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: Flask-SocketIO
2025-07-30 09:18:58 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-30 09:18:58 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV
2025-07-30 09:18:58 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: PaddleOCR
2025-07-30 09:18:58 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-30 09:18:58 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-30 09:18:58 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-30 09:18:58 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-30 09:18:59 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-30 09:18:59 | INFO | data_processing.ocr_processor:<module>:23 | EasyOCR可用
2025-07-30 09:18:59 | INFO | data_processing.ocr_processor:<module>:39 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-30 09:19:02 | INFO | data_processing.ocr_processor:_init_ocr_engines:101 | EasyOCR初始化成功，支持语言: ['en', 'ch_sim']
2025-07-30 09:19:02 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-30 09:19:02 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 'NoneType' object has no attribute 'get'
2025-07-30 09:19:02 | INFO | core.equipment_manager:load_equipment_database:49 | 加载了 5 个设备信息
2025-07-30 09:19:02 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-30 09:19:02 | INFO | core.system_initializer:print_startup_banner:201 | 
======================================================================
2025-07-30 09:19:02 | INFO | core.system_initializer:print_startup_banner:202 | 🚀 故障分析智能助手系统
2025-07-30 09:19:02 | INFO | core.system_initializer:print_startup_banner:203 | ======================================================================
2025-07-30 09:19:02 | INFO | core.system_initializer:print_startup_banner:204 | 📊 版本: 1.0.0
2025-07-30 09:19:02 | INFO | core.system_initializer:print_startup_banner:205 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-30 09:19:02 | INFO | core.system_initializer:print_startup_banner:206 | 🔧 运行模式: 调试模式
2025-07-30 09:19:02 | INFO | core.system_initializer:print_startup_banner:207 | 📁 工作目录: G:\my-dl-dmx
2025-07-30 09:19:02 | INFO | core.system_initializer:print_startup_banner:211 | 📡 WebSocket支持: ❌
2025-07-30 09:19:02 | INFO | core.system_initializer:print_startup_banner:212 | 🤖 AI分析: ✅
2025-07-30 09:19:02 | INFO | core.system_initializer:print_startup_banner:214 | 📊 数据处理: ✅
2025-07-30 09:19:02 | INFO | core.system_initializer:print_startup_banner:215 | 🖼️ 图像处理: ✅
2025-07-30 09:19:02 | INFO | core.system_initializer:print_startup_banner:216 | 📝 OCR识别: ❌
2025-07-30 09:19:02 | INFO | core.system_initializer:print_startup_banner:218 | ======================================================================
2025-07-30 09:19:02 | INFO | core.system_initializer:full_initialization:246 | ✅ 系统初始化完成
2025-07-30 09:19:02 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-30 09:19:02 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-30 09:19:02 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-30 09:19:02 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-30 09:19:02 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-30 09:19:02 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-30 09:19:02 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-30 09:19:02 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-30 09:19:02 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-30 09:19:02 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-30 09:19:02 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-30 09:19:02 | INFO | data_processing.modern_chroma_manager:_init_client:61 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-30 09:19:02 | INFO | data_processing.modern_chroma_manager:_init_collection:83 | ✅ 获取现有集合: power_system_knowledge
2025-07-30 09:19:02 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-30 09:19:02 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-30 09:19:02 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-30 09:19:02 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-30 09:19:02 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-30 09:19:02 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-30 09:19:02 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-30 09:19:02 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-30 09:19:02 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-30 09:19:24 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-30 09:19:24 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-30 09:19:24 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-30 09:19:24 | WARNING | __main__:start_flask_server:95 | SocketIO启动失败，使用标准Flask: run_simple() got an unexpected keyword argument 'allow_unsafe_werkzeug'
2025-07-30 09:19:25 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-30 09:19:25 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-30 09:19:25 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-30 09:19:26 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ Flask-SocketIO 不可用: No module named 'flask_socketio'
2025-07-30 09:19:26 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础HTTP通信
2025-07-30 09:19:32 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddleocr'
2025-07-30 09:19:32 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-30 09:19:32 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-30 09:19:32 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-30 09:19:32 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-30 09:19:32 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-30 09:19:32 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-30 09:19:32 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-30 09:19:32 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, FastAPI, Uvicorn
2025-07-30 09:19:32 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: Flask-SocketIO
2025-07-30 09:19:32 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-30 09:19:32 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV
2025-07-30 09:19:32 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: PaddleOCR
2025-07-30 09:19:32 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-30 09:19:32 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-30 09:19:32 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-30 09:19:32 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-30 09:19:33 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-30 09:19:33 | INFO | data_processing.ocr_processor:<module>:23 | EasyOCR可用
2025-07-30 09:19:33 | INFO | data_processing.ocr_processor:<module>:39 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-30 09:19:35 | INFO | data_processing.ocr_processor:_init_ocr_engines:101 | EasyOCR初始化成功，支持语言: ['ch_sim', 'en']
2025-07-30 09:19:35 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-30 09:19:35 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 'NoneType' object has no attribute 'get'
2025-07-30 09:19:36 | INFO | core.equipment_manager:load_equipment_database:49 | 加载了 5 个设备信息
2025-07-30 09:19:36 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-30 09:19:36 | INFO | core.system_initializer:print_startup_banner:201 | 
======================================================================
2025-07-30 09:19:36 | INFO | core.system_initializer:print_startup_banner:202 | 🚀 故障分析智能助手系统
2025-07-30 09:19:36 | INFO | core.system_initializer:print_startup_banner:203 | ======================================================================
2025-07-30 09:19:36 | INFO | core.system_initializer:print_startup_banner:204 | 📊 版本: 1.0.0
2025-07-30 09:19:36 | INFO | core.system_initializer:print_startup_banner:205 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-30 09:19:36 | INFO | core.system_initializer:print_startup_banner:206 | 🔧 运行模式: 调试模式
2025-07-30 09:19:36 | INFO | core.system_initializer:print_startup_banner:207 | 📁 工作目录: G:\my-dl-dmx
2025-07-30 09:19:36 | INFO | core.system_initializer:print_startup_banner:211 | 📡 WebSocket支持: ❌
2025-07-30 09:19:36 | INFO | core.system_initializer:print_startup_banner:212 | 🤖 AI分析: ✅
2025-07-30 09:19:36 | INFO | core.system_initializer:print_startup_banner:214 | 📊 数据处理: ✅
2025-07-30 09:19:36 | INFO | core.system_initializer:print_startup_banner:215 | 🖼️ 图像处理: ✅
2025-07-30 09:19:36 | INFO | core.system_initializer:print_startup_banner:216 | 📝 OCR识别: ❌
2025-07-30 09:19:36 | INFO | core.system_initializer:print_startup_banner:218 | ======================================================================
2025-07-30 09:19:36 | INFO | core.system_initializer:full_initialization:246 | ✅ 系统初始化完成
2025-07-30 09:19:36 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-30 09:19:36 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-30 09:19:36 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-30 09:19:36 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-30 09:19:36 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-30 09:19:36 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-30 09:19:36 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-30 09:19:36 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-30 09:19:36 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-30 09:19:36 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-30 09:19:36 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-30 09:19:36 | INFO | data_processing.modern_chroma_manager:_init_client:61 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-30 09:19:36 | INFO | data_processing.modern_chroma_manager:_init_collection:83 | ✅ 获取现有集合: power_system_knowledge
2025-07-30 09:19:36 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-30 09:19:36 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-30 09:19:36 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-30 09:19:36 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-30 09:19:36 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-30 09:19:36 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-30 09:19:36 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-30 09:19:36 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-30 09:19:36 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-30 09:19:58 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-30 09:19:58 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-30 09:19:58 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-30 09:19:58 | WARNING | __main__:start_flask_server:95 | SocketIO启动失败，使用标准Flask: run_simple() got an unexpected keyword argument 'allow_unsafe_werkzeug'
2025-07-30 09:21:30 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-30 09:27:37 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-30 09:27:37 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-30 09:27:37 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-30 09:27:38 | ERROR | core.dependency_manager:check_dependency:185 | 加载 Pandas 时发生意外错误: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject
2025-07-30 09:27:41 | ERROR | core.dependency_manager:check_dependency:185 | 加载 Sentence Transformers 时发生意外错误: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject
2025-07-30 09:27:43 | ERROR | core.dependency_manager:check_dependency:185 | 加载 PaddleOCR 时发生意外错误: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject
2025-07-30 09:27:43 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-30 09:27:43 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-30 09:27:43 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-30 09:27:43 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-30 09:27:43 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-30 09:27:43 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-30 09:27:43 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: NumPy, Flask-CORS, Flask-SocketIO, FastAPI, Uvicorn
2025-07-30 09:27:43 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: Pandas
2025-07-30 09:27:43 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-30 09:27:43 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, ChromaDB, OpenCV
2025-07-30 09:27:43 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: Sentence Transformers, PaddleOCR
2025-07-30 09:27:43 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-30 09:27:43 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-30 09:27:43 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-30 09:27:43 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-30 09:27:43 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject
2025-07-30 09:27:43 | WARNING | core.system_initializer:_preload_critical_services:193 | ⚠️ 服务预加载失败: fault_analyzer
2025-07-30 09:27:43 | INFO | core.equipment_manager:load_equipment_database:49 | 加载了 5 个设备信息
2025-07-30 09:27:43 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-30 09:27:43 | INFO | core.system_initializer:print_startup_banner:201 | 
======================================================================
2025-07-30 09:27:43 | INFO | core.system_initializer:print_startup_banner:202 | 🚀 故障分析智能助手系统
2025-07-30 09:27:43 | INFO | core.system_initializer:print_startup_banner:203 | ======================================================================
2025-07-30 09:27:43 | INFO | core.system_initializer:print_startup_banner:204 | 📊 版本: 1.0.0
2025-07-30 09:27:43 | INFO | core.system_initializer:print_startup_banner:205 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-30 09:27:43 | INFO | core.system_initializer:print_startup_banner:206 | 🔧 运行模式: 调试模式
2025-07-30 09:27:43 | INFO | core.system_initializer:print_startup_banner:207 | 📁 工作目录: G:\my-dl-dmx
2025-07-30 09:27:43 | INFO | core.system_initializer:print_startup_banner:211 | 📡 WebSocket支持: ✅
2025-07-30 09:27:43 | INFO | core.system_initializer:print_startup_banner:212 | 🤖 AI分析: ✅
2025-07-30 09:27:43 | INFO | core.system_initializer:print_startup_banner:214 | 📊 数据处理: ❌
2025-07-30 09:27:43 | INFO | core.system_initializer:print_startup_banner:215 | 🖼️ 图像处理: ✅
2025-07-30 09:27:43 | INFO | core.system_initializer:print_startup_banner:216 | 📝 OCR识别: ❌
2025-07-30 09:27:43 | INFO | core.system_initializer:print_startup_banner:218 | ======================================================================
2025-07-30 09:27:43 | INFO | core.system_initializer:full_initialization:246 | ✅ 系统初始化完成
2025-07-30 09:27:43 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-30 09:27:43 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-30 09:27:43 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-30 09:27:43 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-30 09:27:43 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-30 09:27:43 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-30 09:27:43 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-30 09:27:43 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-30 09:27:43 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-30 09:27:43 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-30 09:27:43 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-30 09:27:44 | WARNING | __main__:start_optimized_server:262 | ⚠️ 实时监控启动失败: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject
2025-07-30 09:27:44 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-30 09:27:44 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-30 09:27:44 | ERROR | __main__:start_flask_server:104 | Flask服务器启动失败: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject
2025-07-30 09:27:44 | ERROR | __main__:start_optimized_server:279 | ❌ 主服务器启动失败: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject
2025-07-30 09:27:44 | ERROR | __main__:main:384 | ❌ 系统启动失败: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject
2025-07-30 09:27:44 | INFO | __main__:main:385 | 
❌ 系统启动失败: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject
2025-07-30 09:27:44 | INFO | __main__:main:387 | 
💡 故障排除建议:
2025-07-30 09:27:44 | INFO | __main__:main:388 |    1. 检查端口5002是否被占用
2025-07-30 09:27:44 | INFO | __main__:main:389 |    2. 检查配置文件是否正确
2025-07-30 09:27:44 | INFO | __main__:main:390 |    3. 查看日志文件: logs/startup.log
2025-07-30 09:27:44 | INFO | __main__:main:391 |    4. 尝试重新安装依赖
2025-07-30 09:27:44 | INFO | __main__:main:396 | 系统启动流程结束
