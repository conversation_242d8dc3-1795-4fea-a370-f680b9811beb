2025-07-29 14:42:26 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: 'UnifiedConfigManager' object has no attribute 'get_dict'
2025-07-29 14:42:26 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: 'UnifiedConfigManager' object has no attribute 'get_dict'
2025-07-29 14:43:05 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: 'UnifiedConfigManager' object has no attribute 'get_dict'
2025-07-29 14:43:05 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: 'UnifiedConfigManager' object has no attribute 'get_dict'
2025-07-29 14:44:23 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: 'UnifiedConfigManager' object has no attribute 'get_dict'
2025-07-29 14:44:23 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: 'UnifiedConfigManager' object has no attribute 'get_dict'
2025-07-29 14:45:04 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: 'UnifiedConfigManager' object has no attribute 'get_dict'
2025-07-29 14:45:04 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: 'UnifiedConfigManager' object has no attribute 'get_dict'
2025-07-29 15:29:01 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'cv2'
2025-07-29 15:29:01 | ERROR | data_processing.modern_chroma_manager:__init__:34 | ChromaDB不可用，无法初始化
2025-07-29 15:29:33 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'cv2'
2025-07-29 15:29:34 | ERROR | data_processing.modern_chroma_manager:__init__:34 | ChromaDB不可用，无法初始化
2025-07-29 15:39:13 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'easyocr'
2025-07-29 15:39:43 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'easyocr'
2025-07-29 15:41:17 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 'NoneType' object has no attribute 'get'
2025-07-29 15:42:26 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 'NoneType' object has no attribute 'get'
2025-07-29 15:43:22 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 'NoneType' object has no attribute 'get'
2025-07-29 15:44:16 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 'NoneType' object has no attribute 'get'
2025-07-29 15:45:20 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 'NoneType' object has no attribute 'get'
2025-07-29 15:45:58 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 'NoneType' object has no attribute 'get'
2025-07-30 09:19:02 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 'NoneType' object has no attribute 'get'
2025-07-30 09:19:35 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 'NoneType' object has no attribute 'get'
2025-07-30 09:27:38 | ERROR | core.dependency_manager:check_dependency:185 | 加载 Pandas 时发生意外错误: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject
2025-07-30 09:27:41 | ERROR | core.dependency_manager:check_dependency:185 | 加载 Sentence Transformers 时发生意外错误: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject
2025-07-30 09:27:43 | ERROR | core.dependency_manager:check_dependency:185 | 加载 PaddleOCR 时发生意外错误: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject
2025-07-30 09:27:43 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject
2025-07-30 09:27:44 | ERROR | __main__:start_flask_server:104 | Flask服务器启动失败: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject
2025-07-30 09:27:44 | ERROR | __main__:start_optimized_server:279 | ❌ 主服务器启动失败: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject
2025-07-30 09:27:44 | ERROR | __main__:main:384 | ❌ 系统启动失败: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject
