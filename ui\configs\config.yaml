api:
  deepseek:
    api_key: ${DEEPSEEK_API_KEY}
    base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
    chat_model: deepseek-v3
    max_tokens: 8192
    r1_model: deepseek-r1
    temperature: 0.1
database:
  echo: false
  url: sqlite:///./fault_diagnosis.db
logging:
  file_path: ./logs/app.log
  level: INFO
  retention: 30 days
  rotation: 1 day
security:
  csrf_protection: true
  max_requests_per_minute: 100
  rate_limiting: true
  secret_key: ${SECRET_KEY}
server:
  cors_origins:
  - http://localhost:5002
  - http://127.0.0.1:5002
  host: 0.0.0.0
  port: 5002
  workers: 4
system:
  debug: false
  environment: development
  log_level: INFO
  name: 故障分析智能助手
  version: 2.0.0
upload:
  allowed_extensions:
  - .pdf
  - .docx
  - .txt
  - .jpg
  - .png
  max_file_size: 100
  upload_path: ./uploads
vector_db:
  chroma:
    collection_name: baiyin_power_fault_collection
    persist_directory: ./embeddings/chroma_store_new
  type: chroma
